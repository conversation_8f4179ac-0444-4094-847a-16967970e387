package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type WmsAreaHandler struct {
	*Handler
	wmsAreaService service.WmsAreaService
}

func NewWmsAreaHandler(
	handler *Handler,
	wmsAreaService service.WmsAreaService,
) *WmsAreaHandler {
	return &WmsAreaHandler{
		Handler:        handler,
		wmsAreaService: wmsAreaService,
	}
}

// Create godoc
// @Summary 创建仓库区域
// @Schemes
// @Description 创建新的仓库区域记录
// @Tags 仓储模块,仓库管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.WmsAreaCreateParams true "仓库区域信息"
// @Success 200 {object} v1.Response
// @Router /wms/areas [post]
func (h *WmsAreaHandler) Create(ctx *gin.Context) {
	var req v1.WmsAreaCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsAreaService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新仓库区域
// @Schemes
// @Description 更新指定ID的仓库区域信息
// @Tags 仓储模块,仓库管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "仓库区域ID"
// @Param request body v1.WmsAreaUpdateParams true "仓库区域信息"
// @Success 200 {object} v1.Response
// @Router /wms/areas/{id} [patch]
func (h *WmsAreaHandler) Update(ctx *gin.Context) {
	var req v1.WmsAreaUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsAreaService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除仓库区域
// @Schemes
// @Description 删除指定ID的仓库区域
// @Tags 仓储模块,仓库管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "仓库区域ID"
// @Success 200 {object} v1.Response
// @Router /wms/areas/{id} [delete]
func (h *WmsAreaHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsAreaService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除仓库区域
// @Schemes
// @Description 批量删除指定ID的仓库区域
// @Tags 仓储模块,仓库管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "批量删除参数"
// @Success 200 {object} v1.Response
// @Router /wms/areas [delete]
func (h *WmsAreaHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.wmsAreaService.BatchDelete(ctx, req.Ids); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取仓库区域
// @Schemes
// @Description 获取指定ID的仓库区域信息
// @Tags 仓储模块,仓库管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "仓库区域ID"
// @Success 200 {object} v1.Response{data=v1.WmsAreaResponse}
// @Router /wms/areas/{id} [get]
func (h *WmsAreaHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	area, err := h.wmsAreaService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, area)
}

// List godoc
// @Summary 获取仓库区域列表
// @Schemes
// @Description 获取仓库区域列表，支持分页和筛选
// @Tags 仓储模块,仓库管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param page query int false "页码" default(1)
// @Param pageSize query int false "每页数量" default(10)
// @Param status query string false "状态筛选"
// @Param name query string false "名称筛选"
// @Param code query string false "编号筛选"
// @Param type query string false "类型筛选"
// @Param tenantId query string false "租户ID筛选"
// @Success 200 {object} v1.Response{data=pagination.Result}
// @Router /wms/areas [get]
func (h *WmsAreaHandler) List(ctx *gin.Context) {
	params := pagination.GetParams(ctx)

	result, err := h.wmsAreaService.List(ctx, params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
